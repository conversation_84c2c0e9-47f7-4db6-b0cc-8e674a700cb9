/**
 * A simple utility function for testing
 */
export function greet(name: string): string {
  return `Hello, ${name}!`;
}

/**
 * A utility function to add two numbers
 */
export function add(a: number, b: number): number {
  return a + b;
}

/**
 * A utility function to multiply two numbers
 */
export function multiply(a: number, b: number): number {
  return a * b;
}

/**
 * A class for demonstration purposes
 */
export class Calculator {
  private history: string[] = [];

  add(a: number, b: number): number {
    const result = a + b;
    this.history.push(`${a} + ${b} = ${result}`);
    return result;
  }

  subtract(a: number, b: number): number {
    const result = a - b;
    this.history.push(`${a} - ${b} = ${result}`);
    return result;
  }

  multiply(a: number, b: number): number {
    const result = a * b;
    this.history.push(`${a} * ${b} = ${result}`);
    return result;
  }

  divide(a: number, b: number): number {
    if (b === 0) {
      throw new Error('Division by zero is not allowed');
    }
    const result = a / b;
    this.history.push(`${a} / ${b} = ${result}`);
    return result;
  }

  getHistory(): string[] {
    return [...this.history];
  }

  clearHistory(): void {
    this.history = [];
  }
}

// Default export
export default {
  greet,
  add,
  multiply,
  Calculator
};
