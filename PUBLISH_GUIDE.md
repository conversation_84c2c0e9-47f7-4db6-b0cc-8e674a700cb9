# NPM 发布指南

## 快速开始

### 1. 使用自动化脚本发布

```bash
# 发布补丁版本 (1.0.0 -> 1.0.1)
pnpm run publish:patch

# 发布次要版本 (1.0.0 -> 1.1.0)
pnpm run publish:minor

# 发布主要版本 (1.0.0 -> 2.0.0)
pnpm run publish:major

# 发布到私有仓库
pnpm run publish:private
```

### 2. 手动发布流程

#### 步骤 1: 准备工作
```bash
# 确保代码已提交
git status

# 登录 npm
npm login

# 检查登录状态
npm whoami
```

#### 步骤 2: 测试和构建
```bash
# 运行测试
pnpm test

# 构建项目
pnpm run build

# 代码检查
pnpm run lint
```

#### 步骤 3: 版本管理
```bash
# 更新版本号
npm version patch  # 或 minor, major

# 查看当前版本
npm version
```

#### 步骤 4: 发布
```bash
# 发布到公共仓库
npm publish --access public

# 发布到私有仓库
npm publish --registry https://your-private-registry.com
```

## 私有 NPM 仓库配置

### 方法 1: 全局配置
```bash
# 设置私有仓库地址
npm config set registry https://your-private-registry.com

# 设置认证 token
npm config set //your-private-registry.com/:_authToken your-auth-token
```

### 方法 2: 项目级配置
创建 `.npmrc` 文件：
```
registry=https://your-private-registry.com
//your-private-registry.com/:_authToken=your-auth-token
```

### 方法 3: Scope 配置
```bash
# 只为特定 scope 设置私有仓库
npm config set @your-scope:registry https://your-private-registry.com
```

## 常用私有仓库解决方案

### 1. Verdaccio (推荐用于小团队)
```bash
# 安装
npm install -g verdaccio

# 启动
verdaccio

# 默认地址: http://localhost:4873
```

### 2. npm Enterprise
- 官方企业版解决方案
- 完整的权限管理
- 高可用性

### 3. Nexus Repository
- 支持多种包管理器
- 企业级功能
- 详细的访问控制

## 发布检查清单

- [ ] 代码已提交到 git
- [ ] 所有测试通过
- [ ] 代码通过 lint 检查
- [ ] 版本号已更新
- [ ] README.md 已更新
- [ ] CHANGELOG.md 已更新（如果有）
- [ ] 已登录到正确的 npm 仓库
- [ ] package.json 中的 files 字段正确
- [ ] 构建产物存在且正确

## 常见问题

### 1. 发布失败：包名已存在
```bash
# 检查包名是否可用
npm view your-package-name

# 解决方案：使用 scope 或更改包名
```

### 2. 权限错误
```bash
# 检查登录状态
npm whoami

# 重新登录
npm logout
npm login
```

### 3. 私有包发布到公共仓库
确保 package.json 中设置：
```json
{
  "publishConfig": {
    "access": "restricted",
    "registry": "https://your-private-registry.com"
  }
}
```

## 版本管理最佳实践

### 语义化版本 (SemVer)
- **MAJOR**: 不兼容的 API 修改
- **MINOR**: 向后兼容的功能性新增
- **PATCH**: 向后兼容的问题修正

### 预发布版本
```bash
# 发布 beta 版本
npm version prerelease --preid=beta
npm publish --tag beta

# 发布 alpha 版本
npm version prerelease --preid=alpha
npm publish --tag alpha
```

## 安全注意事项

1. **不要在代码中包含敏感信息**
2. **使用 .npmignore 排除不必要的文件**
3. **定期更新依赖包**
4. **使用 npm audit 检查安全漏洞**

```bash
# 安全审计
npm audit

# 自动修复
npm audit fix
```
