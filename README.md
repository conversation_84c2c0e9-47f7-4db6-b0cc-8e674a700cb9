# Test NPM Library

A test npm library for private registry testing.

## Installation

```bash
npm install @your-scope/test-npm-library
```

## Usage

### ES Modules

```javascript
import { greet, add, multiply, Calculator } from '@your-scope/test-npm-library';

// Use utility functions
console.log(greet('World')); // Hello, World!
console.log(add(2, 3)); // 5
console.log(multiply(4, 5)); // 20

// Use Calculator class
const calc = new Calculator();
console.log(calc.add(10, 5)); // 15
console.log(calc.subtract(10, 3)); // 7
console.log(calc.getHistory()); // ['10 + 5 = 15', '10 - 3 = 7']
```

### CommonJS

```javascript
const { greet, add, multiply, Calculator } = require('@your-scope/test-npm-library');

// Same usage as above
```

## API

### Functions

- `greet(name: string): string` - Returns a greeting message
- `add(a: number, b: number): number` - Adds two numbers
- `multiply(a: number, b: number): number` - Multiplies two numbers

### Calculator Class

- `add(a: number, b: number): number` - Adds two numbers and records in history
- `subtract(a: number, b: number): number` - Subtracts two numbers and records in history
- `multiply(a: number, b: number): number` - Multiplies two numbers and records in history
- `divide(a: number, b: number): number` - Divides two numbers and records in history
- `getHistory(): string[]` - Returns calculation history
- `clearHistory(): void` - Clears calculation history

## Development

```bash
# Install dependencies
npm install

# Build the library
npm run build

# Run tests
npm test

# Run tests in watch mode
npm run test:watch

# Lint code
npm run lint

# Fix linting issues
npm run lint:fix
```

## License

MIT
