import { greet, add, multiply, Calculator } from '../src/index';

describe('Utility Functions', () => {
  test('greet function should return correct greeting', () => {
    expect(greet('World')).toBe('Hello, <PERSON>!');
    expect(greet('Alice')).toBe('Hello, <PERSON>!');
  });

  test('add function should add two numbers correctly', () => {
    expect(add(2, 3)).toBe(5);
    expect(add(-1, 1)).toBe(0);
    expect(add(0, 0)).toBe(0);
  });

  test('multiply function should multiply two numbers correctly', () => {
    expect(multiply(2, 3)).toBe(6);
    expect(multiply(-2, 3)).toBe(-6);
    expect(multiply(0, 5)).toBe(0);
  });
});

describe('Calculator Class', () => {
  let calculator: Calculator;

  beforeEach(() => {
    calculator = new Calculator();
  });

  test('should add numbers correctly', () => {
    expect(calculator.add(2, 3)).toBe(5);
    expect(calculator.add(-1, 1)).toBe(0);
  });

  test('should subtract numbers correctly', () => {
    expect(calculator.subtract(5, 3)).toBe(2);
    expect(calculator.subtract(1, 1)).toBe(0);
  });

  test('should multiply numbers correctly', () => {
    expect(calculator.multiply(2, 3)).toBe(6);
    expect(calculator.multiply(-2, 3)).toBe(-6);
  });

  test('should divide numbers correctly', () => {
    expect(calculator.divide(6, 2)).toBe(3);
    expect(calculator.divide(5, 2)).toBe(2.5);
  });

  test('should throw error when dividing by zero', () => {
    expect(() => calculator.divide(5, 0)).toThrow('Division by zero is not allowed');
  });

  test('should track calculation history', () => {
    calculator.add(2, 3);
    calculator.multiply(4, 5);
    
    const history = calculator.getHistory();
    expect(history).toHaveLength(2);
    expect(history[0]).toBe('2 + 3 = 5');
    expect(history[1]).toBe('4 * 5 = 20');
  });

  test('should clear history', () => {
    calculator.add(2, 3);
    calculator.clearHistory();
    
    expect(calculator.getHistory()).toHaveLength(0);
  });
});
