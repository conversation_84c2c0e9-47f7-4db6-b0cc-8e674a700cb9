#!/usr/bin/env node

const { execSync } = require("child_process");
const fs = require("fs");
const path = require("path");

// 颜色输出
const colors = {
  red: "\x1b[31m",
  green: "\x1b[32m",
  yellow: "\x1b[33m",
  blue: "\x1b[34m",
  reset: "\x1b[0m",
};

function log(message, color = "reset") {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

function exec(command, options = {}) {
  try {
    return execSync(command, { stdio: "inherit", ...options });
  } catch (error) {
    log(`Command failed: ${command}`, "red");
    process.exit(1);
  }
}

function main() {
  const args = process.argv.slice(2);
  const versionType = args[0] || "patch";
  const registry = args[1];

  log("🚀 Starting publish process...", "blue");

  // 1. 检查工作目录是否干净
  try {
    execSync("git diff-index --quiet HEAD --", { stdio: "pipe" });
  } catch (error) {
    log(
      "❌ Working directory is not clean. Please commit your changes first.",
      "red"
    );
    process.exit(1);
  }

  // // 2. 运行测试
  // log('🧪 Running tests...', 'yellow');
  // exec('pnpm test');

  // 3. 运行构建
  log("🔨 Building project...", "yellow");
  exec("pnpm run build");

  // // 4. 运行 lint
  // log('🔍 Running lint...', 'yellow');
  // exec('pnpm run lint');

  // 5. 更新版本
  log(`📦 Updating version (${versionType})...`, "yellow");
  exec(`npm version ${versionType}`);

  // 6. 读取新版本号
  const packageJson = JSON.parse(fs.readFileSync("package.json", "utf8"));
  const newVersion = packageJson.version;
  log(`✅ Version updated to ${newVersion}`, "green");

  // 7. 发布
  log("📤 Publishing to npm...", "yellow");
  const publishCommand = registry
    ? `npm publish --registry ${registry}`
    : "npm publish";

  exec(publishCommand);

  // 8. 推送到 git
  log("📤 Pushing to git...", "yellow");
  exec("git push");
  exec("git push --tags");

  log(`🎉 Successfully published version ${newVersion}!`, "green");
}

if (require.main === module) {
  main();
}
