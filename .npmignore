# Source files
src/
tests/
*.test.ts
*.spec.ts

# Configuration files
tsconfig.json
rollup.config.js
jest.config.js
.eslintrc.js

# Development dependencies
node_modules/
coverage/

# IDE
.vscode/
.idea/
*.swp
*.swo

# OS
.DS_Store
Thumbs.db

# Git
.git/
.gitignore

# CI/CD
.github/
.travis.yml
.circleci/

# Documentation (keep README.md)
docs/

# Logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
