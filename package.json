{"name": "@chengzhaoxi/test-npm-library", "version": "0.0.1", "description": "A test npm library for private registry testing", "main": "dist/index.js", "module": "dist/index.esm.js", "types": "dist/index.d.ts", "files": ["dist", "README.md", "LICENSE"], "scripts": {"build": "rollup -c", "dev": "rollup -c -w", "test": "jest", "test:watch": "jest --watch", "lint": "eslint src --ext .ts,.js", "lint:fix": "eslint src --ext .ts,.js --fix", "prepublishOnly": "npm run build && npm run test", "prepare": "npm run build", "publish:patch": "node scripts/publish.js patch", "publish:minor": "node scripts/publish.js minor", "publish:major": "node scripts/publish.js major", "publish:private": "node scripts/publish.js patch https://your-private-registry.com"}, "keywords": ["test", "npm", "library", "private"], "author": "chengzhaoxi <EMAIL>", "license": "MIT", "devDependencies": {"@rollup/plugin-commonjs": "^25.0.0", "@rollup/plugin-node-resolve": "^15.0.0", "@rollup/plugin-typescript": "^11.0.0", "@types/jest": "^29.0.0", "@typescript-eslint/eslint-plugin": "^6.0.0", "@typescript-eslint/parser": "^6.0.0", "eslint": "^8.0.0", "jest": "^29.0.0", "rollup": "^3.0.0", "ts-jest": "^29.0.0", "tslib": "^2.6.0", "typescript": "^5.0.0"}, "publishConfig": {"access": "restricted"}, "repository": {"type": "git", "url": "git+https://github.com/your-username/test-npm-library.git"}, "bugs": {"url": "https://github.com/your-username/test-npm-library/issues"}, "homepage": "https://github.com/your-username/test-npm-library#readme"}